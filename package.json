{"name": "attendance-system", "version": "1.0.0", "description": "Attendacne System For Employee Back-End (NodeJS,MogoDB,Express)", "repository": {"type": "git", "url": "Attendance-System"}, "license": "ISC", "author": "<PERSON>", "type": "commonjs", "main": "app.js", "scripts": {"start": "node server.js", "start:prod": "set NODE_ENV=production && nodemon server.js"}, "dependencies": {"bcryptjs": "^3.0.1", "cloudinary": "^2.6.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-fileupload": "^1.5.1", "express-mongo-sanitize": "^1.3.2", "express-rate-limit": "^3.5.3", "helmet": "^3.23.3", "hpp": "^0.2.3", "jsonwebtoken": "^8.5.1", "mongodb": "^6.12.0", "mongoose": "^5.13.23", "morgan": "^1.10.0", "slugify": "^1.6.6", "xss-clean": "^0.1.4"}, "devDependencies": {"eslint": "^5.16.0", "eslint-config-airbnb": "^17.1.1", "eslint-config-prettier": "^4.3.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-node": "^8.0.1", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.37.4", "parcel-bundler": "^1.12.5", "prettier": "^1.19.1"}, "engines": {"node": ">=10.0.0"}}